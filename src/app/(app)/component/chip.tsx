interface ChipProps {
  label: string;
  className?: string;
  title?: string;
}

export const Chip = ({ label, className = "text-base bg-gray-300", title }: ChipProps) => {
  return (
    <span
      title={title ?? label}
      className={`inline-block  whitespace-nowrap rounded-1.8 px-2 py-1 text-center align-baseline text-xs font-bold uppercase leading-none ${className} `}
    >
      {label}
    </span>
  );
};
