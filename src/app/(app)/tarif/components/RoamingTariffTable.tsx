"use client";

import { AgGridReact } from "ag-grid-react";
import React, { useState } from "react";

import "ag-grid-community/dist/styles/ag-grid.css";
import "ag-grid-community/dist/styles/ag-theme-alpine.css";
import type { GridOptions, ICellRendererParams } from "ag-grid-community";
import type {
  ColDef,
  ColGroupDef,
  ValueGetterParams,
} from "ag-grid-community/dist/lib/entities/colDef";
import Link from "next/link";
import { TfiPencil } from "react-icons/tfi";
import { MdBookmarkAdd } from "react-icons/md";
import { KindOfTarif } from "@prisma/client";
import { Chip } from "~/app/(app)/component/chip";
import Table from "~/utils/table/table";

export const RoamingTariffTable = ({ data }: any) => {
  const CellRenderer = (params: ICellRendererParams) => {
    return (
      <>
        <Link href={`tarif/roaming/update/${params.data?.id}`}>
          <TfiPencil />
        </Link>
        {params.data.kindOfTarif != KindOfTarif.DIRECT && (
          <Link href={`tarif/mapping/${params.data?.id}`} title={"Abonennten verwalten"}>
            <MdBookmarkAdd />
          </Link>
        )}
      </>
    );
  };

  const columnDefs = [
    {
      field: "id",
      width: 150,
      minWidth: 120,
      headerName: "Tariftyp",
      valueGetter: (params: ValueGetterParams) => params?.data?.kindOfTarif,
      cellRenderer: (params: ICellRendererParams) => {
        if (params.data?.kindOfTarif == KindOfTarif.ROAMING) {
          return <Chip label={"Roaming"} />;
        } else if (params.data?.kindOfTarif == KindOfTarif.DIRECT) {
          return <Chip label={"Direct"} className={"bg-gray-300 brightness-90"} />;
        }
        return <Chip label={"Tarif"} />;
      },
    },
    { field: "name", width: 400 },
    { field: "sessionFee", headerName: "Preis pro Session" },
    { field: "kwh" },
    { field: "minChargingEnergy", headerName: "Mindest Lademenge in kWh" },
    { field: "currentType", headerName: "Ladepunkt-Type (AC/DC)" },
    {
      field: "validFrom",
      headerName: "Gültig ab",
      cellRenderer: (params: ICellRendererParams) =>
        `${new Date(params.data?.validFrom).toLocaleDateString("de-DE")}`,
    },
    {
      field: "validTo",
      headerName: "Gültig bis",
      cellRenderer: (params: ICellRendererParams) =>
        `${new Date(params.data?.validTo).toLocaleDateString("de-DE")}`,
    },
    {
      field: "_count",
      headerName: "Abonnierte",
      cellRenderer: (params: ICellRendererParams) => `${params.data?._count.contacts}`,
    },
    { field: "action", headerName: "Aktion", cellRenderer: CellRenderer },
  ];

  return <Table gridId={"roamingtarif"} columnDefs={columnDefs} rowData={data} />;
};

export default RoamingTariffTable;
