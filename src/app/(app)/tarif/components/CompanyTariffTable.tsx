"use client";
import React, { useState } from "react";
import Table from "~/utils/table/table";
import type { ICellRendererParams } from "ag-grid-community";
import Link from "next/link";
import { TfiPencil } from "react-icons/tfi";

export const CompanyTariffTable = ({ data }: any) => {
  const CellRenderer = (params: ICellRendererParams) => {
    return (
      <>
        {!params.data.optional ? (
          <Link href={`/tarif/company/update/${params.data.id}`}>
            <TfiPencil title={"Editieren"} />
          </Link>
        ) : (
          <TfiPencil
            color={"gray"}
            className={"hover:cursor-not-allowed"}
            title={"Editieren nicht erlaubt"}
          />
        )}
      </>
    );
  };

  const UserGroupCellRenderer = (params: ICellRendererParams) => {
    const hasUserGroups = params.data.userGroups && params.data.userGroups.length > 0;

    return (
      <div className="flex items-center">
        {hasUserGroups ? (
          <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
            ✓ Zugeordnet
          </span>
        ) : (
          <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
            Nicht zugeordnet
          </span>
        )}
      </div>
    );
  };

  const columnDefs = [
    { field: "currentType", headerName: "AC/DC", width: 100, floatingFilter: false },
    { field: "name", headerName: "Name", width: 400, floatingFilter: false },
    { field: "sessionPrice", headerName: "Startgebühr", width: 400, floatingFilter: false },
    { field: "energyPrice", headerName: "kWh", width: 400, floatingFilter: false },
    { field: "blockingFee", headerName: "Blockiergebühren", floatingFilter: false },
    { field: "blockingFeeMax", headerName: "max Blockiergebühren", floatingFilter: false },
    {
      field: "blockingFeeBeginAtMin",
      headerName: "Blockiergebühren ab min",
      floatingFilter: false,
    },
    {
      field: "userGroups",
      headerName: "Tarifgruppen",
      width: 150,
      cellRenderer: UserGroupCellRenderer,
      floatingFilter: false,
    },
    //todo editieren soll noch nicht möglich sein
    // { field: "action", headerName: "Aktion", cellRenderer: CellRenderer, floatingFilter: false },
  ];
  return <Table columnDefs={columnDefs} rowData={data} />;
};
