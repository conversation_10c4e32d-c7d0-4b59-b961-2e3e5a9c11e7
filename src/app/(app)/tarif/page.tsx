import React from "react";

import { RoamingTariffTable } from "./components/RoamingTariffTable";
import { CreditTariffTable } from "./components/CreditTariffTable";
import { NewTariffButton } from "~/app/(app)/tarif/components/NewTariffButton";
export const revalidate = 0;

import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { CompanyTariffTable } from "~/app/(app)/tarif/components/CompanyTariffTable";
import { Role } from "@prisma/client";
import SweatAlert from "~/component/sweatAlert";
import NotFound from "~/app/(app)/not-found";

async function getRoamingTariffs() {
  const session = await getServerSession(authOptions);
  const tarif = await prisma.tarif.findMany({
    include: {
      _count: true,
    },
  });
  return JSON.parse(JSON.stringify(tarif));
}

async function getCreditTariffs() {
  const creditTariffs = await prisma.creditTarif.findMany();
  return creditTariffs;
}

async function getCompanyTariffs() {
  const companyTariffs = await prisma.companyTarif.findMany();
  return companyTariffs;
}

export default async function Page() {
  const roamingTariffs = await getRoamingTariffs();
  const creditTariffs = await getCreditTariffs();
  const companyTariffs = await getCompanyTariffs();
  const session = await getServerSession(authOptions);
  if (!(session?.user?.role == Role.ADMIN)) {
    return <NotFound />;
  }
  return (
    <>
      <div className={""}>
        <>
          <div
            className="relative mt-6 flex min-w-0 flex-col break-words rounded-2xl border-0 bg-white bg-clip-border p-5 shadow-soft-xl dark:bg-gray-950 dark:shadow-soft-dark-xl"
            id="romaing-offer-table"
          >
            <div className=" mb-4 flex flex-col sm:flex-row sm:justify-between">
              <h4 className="sm:text-md mb-2 text-base text-primary">
                Übersicht Roaming & Direct Tarife
              </h4>
              <span>
                <NewTariffButton href={"/tarif/roaming/new"} label={"Neuer Roaming/Direct Tarif"} />
              </span>
            </div>
            <RoamingTariffTable data={roamingTariffs} />
          </div>

          <div
            className="relative mt-6 flex min-w-0 flex-col break-words rounded-2xl border-0 bg-white bg-clip-border p-5 shadow-soft-xl dark:bg-gray-950 dark:shadow-soft-dark-xl"
            id="credit-tariff-table"
          >
            <div className="mb-4 flex flex-col sm:flex-row sm:justify-between">
              <h4 className="sm:text-md mb-2 text-base text-primary">
                Übersicht der Gutschrift (CPO) Tarife
              </h4>
              <span>
                <NewTariffButton
                  href={"/tarif/credit/new"}
                  label={"Neuer Gutschrift (CPO) Tarif"}
                />
              </span>
            </div>
            <CreditTariffTable data={creditTariffs} />
          </div>
        </>

        <div
          className="relative mt-6 flex min-w-0 flex-col break-words rounded-2xl border-0 bg-white bg-clip-border p-5 shadow-soft-xl dark:bg-gray-950 dark:shadow-soft-dark-xl"
          id="romaing-offer-table"
        >
          <div className=" mb-4 flex flex-col sm:flex-row sm:justify-between">
            <h4 className="sm:text-md mb-2 text-base text-primary">Übersicht Firmentarife</h4>
            <span>
              <NewTariffButton href={"/tarif/company/new"} label={"Neuer Firmentarif"} />
            </span>
          </div>
          <CompanyTariffTable data={companyTariffs} />
        </div>
      </div>
    </>
  );
}
