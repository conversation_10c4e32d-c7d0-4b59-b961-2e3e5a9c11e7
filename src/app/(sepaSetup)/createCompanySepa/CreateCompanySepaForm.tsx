"use client";
import { IbanElement, PaymentElement, useElements, useStripe } from "@stripe/react-stripe-js";
import React, { useEffect, useState } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import type { StripeIbanElementChangeEvent } from "@stripe/stripe-js";
import { useRouter } from "next/navigation";
import Button from "~/component/button";
import { FiLoader } from "react-icons/fi";
import { FaCheck } from "react-icons/fa";
import { ContactsWithAddress } from "~/types/prisma/contact";
import { getContactAdressByDate } from "~/utils/contact/getContactAdressByDate";
import { ISO3166_country_codes } from "~/utils/format/countrycodes";

interface Props {
  clientSecret: string;
  contact?: ContactsWithAddress;
}

interface FormData {
  email: string;
  firstName: string;
  lastName: string;
  companyname: string;
  city: string;
  street: string;
  streetNr: string;
  country: string;
  zip: string;
}

export const CreateCompanySepaForm = ({ clientSecret: clientSecret, contact: contact }: Props) => {
  const stripe = useStripe();
  const elements = useElements();
  const [responseCode, setResponseCode] = useState<boolean>(false);

  const address = getContactAdressByDate(contact?.contactAddress ?? [], new Date());
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isSubmitSuccessful },
  } = useForm<FormData>({
    defaultValues: {
      companyname: contact?.companyName ?? "",
      email: contact?.invoiceMail,
      city: address?.city ? address.city : "",
      zip: address?.zip ? address.zip : "",
      country: address?.country ? address.country : "",
      street: address?.street ? address.street : "",
      streetNr: address?.streetNr ? address.streetNr : "",
    },
  });
  const [errorMessageIban, setErrorMessageIban] = useState("");
  const [sepaResult, setSepaResult] = useState<string>("");

  const [disabled, setDisabled] = useState(true);
  const router = useRouter();
  useEffect(() => {
    setDisabled(!stripe || Object.keys(errors).length > 0 || errorMessageIban.length > 0);
  }, [stripe, errorMessageIban, errors]);

  const onSubmit: SubmitHandler<FormData> = async (data) => {
    if (!stripe || !elements) {
      // Stripe.js hasn't yet loaded.
      // Make sure to disable form submission until Stripe.js has loaded.
      return;
    }

    const iban = elements.getElement(IbanElement);
    let contactAddress;
    if (contact?.contactAddress) {
      contactAddress = getContactAdressByDate(contact?.contactAddress, new Date());
    }
    if (iban) {
      let countryCode;
      if (contactAddress?.country) {
        countryCode = ISO3166_country_codes[contactAddress?.country];
      }

      const result = await stripe.confirmSepaDebitSetup(clientSecret, {
        payment_method: {
          sepa_debit: iban,
          billing_details: {
            name: `${data.companyname} (${data.firstName} ${data.lastName}) `,
            email: data.email,
            address: {
              city: contactAddress?.city ?? "",
              country: countryCode,
              line1: contactAddress?.street ?? "",
              line2: contactAddress?.streetNr ?? "",
              postal_code: contactAddress?.zip ?? "",
              state: contactAddress?.country ?? "",
            },
          },
        },
      });

      if (result.error) {
        // Show error to your customer.
        setSepaResult(result.error.message ?? "Fehler beim erteilen des Lastschriftmandates");
      } else {
        const paymentMethod = result.setupIntent.payment_method;
        const paymentMethodId =
          typeof paymentMethod === "object" ? paymentMethod?.id : paymentMethod;

        const response = await fetch(
          `/api/stripe/setDefaultCompanyPaymentMethod?contactId=${contact?.id}&paymentMethodId=${result.setupIntent.payment_method}&sepaHash=${contact?.sepaHash}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          },
        );

        if (response.ok) {
          const responseData = await response.json();
          if (responseData?.success) {
            setResponseCode(responseData.success);

            // Create admin notification for company SEPA mandate setup
            try {
              await fetch('/api/admin/notifications/send', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  message: `Firmen-SEPA-Lastschriftmandat hinterlegt von ${contact?.companyName || contact?.name}`,
                  type: 'INFO',
                  targetType: 'role',
                  targetId: 'ADMIN',
                }),
              });
            } catch (error) {
              console.error('Failed to send admin notification:', error);
            }
          }
        }
      }
    }
  };
  const handleIbanChange = (event: StripeIbanElementChangeEvent) => {
    if (event.error) {
      setErrorMessageIban(event.error.message);
    } else {
      setErrorMessageIban(""); // clear any existing error message
      setSepaResult("");
    }
  };

  return (
    <>
      <form
        className={`${isSubmitSuccessful && responseCode ? "hidden" : ""}`}
        id={"payment-form"}
        onSubmit={handleSubmit(onSubmit)}
      >
        <div className={"w-full 2xl:w-2/3"}>
          <label
            className="ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
            htmlFor="firstName"
          >
            IBAN:
          </label>
          <IbanElement
            onChange={handleIbanChange}
            className="w-full rounded-xl border-[1px] border-gray-300 bg-white px-1 py-3  "
            options={{ supportedCountries: ["SEPA"] }}
          />
          {errorMessageIban && <span className="ml-1 text-red-500">{errorMessageIban}</span>}
        </div>

        <div className="mt-5 flex w-full flex-col md:flex-row  md:space-x-3 2xl:w-2/3">
          <div className="w-full lg:w-1/2">
            <label
              className="ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="firstName"
            >
              Vorname:
            </label>
            <input
              id={"firstName"}
              placeholder={"Vorname"}
              {...register("firstName", { required: true })}
              className="w-full rounded-xl border-[1px] border-gray-300 p-2"
            />
            {errors.firstName && (
              <span className="ml-1 text-red-500">Ein Vorname ist erforderlich</span>
            )}
          </div>
          <div className="w-full lg:w-1/2">
            <label
              className="ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="lastName"
            >
              Nachname:
            </label>
            <input
              id={"lastName"}
              placeholder={"Nachname"}
              {...register("lastName", { required: true })}
              className="w-full rounded-xl border-[1px] border-gray-300 p-2"
            />
            {errors.lastName && (
              <span className="ml-1 text-red-500">Ein Nachname ist erforderlich</span>
            )}
          </div>
        </div>
        <div className="mt-5 flex w-full flex-col md:flex-row  md:space-x-3 2xl:w-2/3">
          <div className="w-full lg:w-1/2">
            <label
              className="ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="company"
            >
              Unternehmen:
            </label>
            <input
              id={"company"}
              readOnly={true}
              placeholder={"Unternehmen"}
              {...register("companyname", { required: true })}
              className="w-full rounded-xl border-[1px] border-gray-300 p-2 read-only:bg-gray-200 hover:cursor-not-allowed"
            />
            {errors.companyname && (
              <span className="ml-1 text-red-500">Die Firmenbezeichnung ist erforderlich</span>
            )}
          </div>
          <div className="w-full lg:w-1/2">
            <label
              className="ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="email"
            >
              Email:
            </label>
            <input
              id={"email"}
              readOnly={true}
              placeholder={"E-Mail"}
              {...register("email", {
                required: true,
                pattern: /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/,
              })}
              className="w-full rounded-xl border-[1px] border-gray-300 p-2 read-only:bg-gray-200 hover:cursor-not-allowed"
            />
            {errors.email && (
              <span className="ml-1 text-red-500">Geben Sie eine gültige E-Mail-Adresse ein</span>
            )}
          </div>
        </div>
        <div className="mt-1 flex w-full flex-col md:flex-row  md:space-x-3 2xl:w-2/3">
          <div className="w-full lg:w-2/4">
            <label
              className="ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="street"
            >
              Straße:
            </label>
            <input
              id={"street"}
              readOnly={true}
              placeholder={"Straße"}
              {...register("street", { required: true })}
              className="w-full rounded-xl border-[1px] border-gray-300 p-2 read-only:bg-gray-200 hover:cursor-not-allowed"
            />
            {errors.street && (
              <span className="ml-1 text-red-500">Die Straße ist erforderlich</span>
            )}
          </div>
          <div className="w-full lg:w-1/4">
            <label
              className="ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="streetNr"
            >
              Hausnummer:
            </label>
            <input
              id={"streetNr"}
              readOnly={true}
              placeholder={"Hausnummer"}
              {...register("streetNr", { required: true })}
              className="w-full rounded-xl border-[1px] border-gray-300 p-2 read-only:bg-gray-200 hover:cursor-not-allowed"
            />
            {errors.streetNr && (
              <span className="ml-1 text-red-500">Die Hausnummer ist erforderlich</span>
            )}
          </div>
          <div className="w-full lg:w-1/4">
            <label
              className="ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="zip"
            >
              Postleitzahl:
            </label>
            <input
              id={"zip"}
              readOnly={true}
              placeholder={"Postleitzahl"}
              {...register("zip", { required: true })}
              className="w-full rounded-xl border-[1px] border-gray-300 p-2 read-only:bg-gray-200 hover:cursor-not-allowed"
            />
            {errors.zip && (
              <span className="ml-1 text-red-500">Die Postleitzahl ist erforderlich</span>
            )}
          </div>
        </div>
        <div className="mt-1 flex w-full flex-col  md:flex-row  md:space-x-3 2xl:w-2/3">
          <div className="w-full lg:w-1/2">
            <label
              className="ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="city"
            >
              Stadt:
            </label>
            <input
              id={"city"}
              readOnly={true}
              placeholder={"Stadt"}
              {...register("city", { required: true })}
              className="w-full rounded-xl border-[1px] border-gray-300 p-2 read-only:bg-gray-200 hover:cursor-not-allowed"
            />
            {errors.city && <span className="ml-1 text-red-500">Die Stadt ist erforderlich</span>}
          </div>
          <div className="w-full lg:w-1/2">
            <label
              className="ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="country"
            >
              Land:
            </label>
            <input
              id={"country"}
              readOnly={true}
              placeholder={"Land"}
              {...register("country", { required: true })}
              className="w-full rounded-xl border-[1px] border-gray-300 p-2 read-only:bg-gray-200 hover:cursor-not-allowed"
            />
            {errors.country && <span className="ml-1 text-red-500">Das Land ist erforderlich</span>}
          </div>
        </div>

        <div className={"mt-4 block text-eul-gray"}>
          *Durch Angabe Ihrer Zahlungsinformationen und der Bestätigung der vorliegenden Zahlung
          ermächtigen Sie (A) Eulektro GmbH und Stripe, unseren Zahlungsdienstleister, Ihrem
          Kreditinstitut Anweisungen zur Belastung Ihres Kontos zu erteilen, und (B) Ihr
          Kreditinstitut, Ihr Konto gemäß diesen Anweisungen zu belasten. Im Rahmen Ihrer Rechte
          haben Sie, entsprechend den Vertragsbedingungen mit Ihrem Kreditinstitut, Anspruch auf
          eine Rückerstattung von Ihrem Kreditinstitut. Eine Rückerstattung muss innerhalb von 8
          Wochen ab dem Tag, an dem Ihr Konto belastet wurde, geltend gemacht werden. Eine
          Erläuterung Ihrer Rechte können Sie von Ihrem Kreditinstitut anfordern. Sie erklären sich
          einverstanden, Benachrichtigungen über künftige Belastungen bis spätestens 2 Tage vor dem
          Buchungsdatum zu erhalten.
        </div>

        <div
          className={
            "sm:max-w-150 mt-3 flex w-full flex-col-reverse items-center justify-center sm:flex-row sm:justify-start"
          }
        >
          <Button
            disabled={isSubmitting || (isSubmitSuccessful && responseCode)}
            className={"w-full sm:max-w-100"}
          >
            {isSubmitting ? (
              <>
                {" "}
                prüfen.. <FiLoader className="animate-spin" />
              </>
            ) : (
              "Bestätigen*"
            )}
          </Button>

          <span className={"ml-2 text-red-500"}> {sepaResult}</span>
        </div>
        {isSubmitSuccessful && !responseCode && (
          <h5 className={"text-red-500"}> Fehler beim hinterlegen das Mandates!</h5>
        )}
      </form>
      {isSubmitSuccessful && responseCode && (
        <h5 className={"flex flex-row items-center gap-2 text-green-500"}>
          <FaCheck className={"h-full"} />
          <span>
            Lastschriftmandat erfolgreich hinterlegt! Sie können diese Seite nun schließen.
          </span>
        </h5>
      )}
    </>
  );
};
