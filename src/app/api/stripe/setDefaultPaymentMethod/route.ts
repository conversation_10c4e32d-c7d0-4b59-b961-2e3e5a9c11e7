import { type NextRequest, NextResponse } from "next/server";

import { stripe } from "~/utils/stripe/stripe";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { activateCard } from "~/utils/user/empcard";
import { sendAcknowledgeMail } from "~/utils/email";
import { createSystemNotificationForAdmins } from "~/utils/notifications/createSystemNotification";
import { NotificationType } from "@prisma/client";

export const revalidate = 0;

export async function GET(request: NextRequest) {
  const payment_method_id = request.nextUrl.searchParams.get("paymentMethodId");

  if (!payment_method_id) {
    return NextResponse.json(
      { success: false, message: "No PaymentMethodId provided" },
      { status: 401 },
    );
  }
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 404 });
  }
  //todo zod post verfiy
  const id = session?.user.id;
  const user = await prisma.user.findUnique({
    where: {
      id: id,
    },
    include: { selectedOu: true },
  });
  if (!user) {
    return NextResponse.json({ success: false, message: "User not found" }, { status: 404 });
  }
  if (user.stripeCustomerId) {
    const updatedCustomer = await stripe.customers.update(user.stripeCustomerId, {
      invoice_settings: {
        default_payment_method: payment_method_id,
      },
    });
    if (updatedCustomer.invoice_settings.default_payment_method === payment_method_id) {
      // in case a card was used for register, they are deaktivated until a payment method
      // is defined. So look for inactive cards with activatedAt date = null an activate it
      const cardsToBeActivated = await prisma.eMPCard.findMany({
        where: {
          userId: user?.id,
          preDelivered: true,
          active: false,
          deactivatedAt: null,
          activatedAt: null,
          physicalCard: {
            valid: false,
          },
        },
        include: {
          physicalCard: true,
          contact: true,
        },
      });
      for (const card of cardsToBeActivated) {
        if (card.id && card?.physicalCard?.visualNumber) {
          await activateCard(card.id, card?.physicalCard?.visualNumber);
          await sendAcknowledgeMail(user, card);
        }
      }

      // Create admin notification for SEPA mandate setup
      await createSystemNotificationForAdmins({
        nachricht: `SEPA-Lastschriftmandat hinterlegt von ${user.name} ${user.lastName}`,
        type: NotificationType.INFO,
      });

      return NextResponse.json(
        { success: true, message: "Default Payment Method updated" },
        { status: 200 },
      );
    }
    return NextResponse.json(
      {
        success: false,
        message: "Error changing default payment method (update gone wrong)",
      },
      { status: 500 },
    );
  }

  return NextResponse.json(
    { success: false, message: "Error changing default payment method" },
    { status: 500 },
  );
}
