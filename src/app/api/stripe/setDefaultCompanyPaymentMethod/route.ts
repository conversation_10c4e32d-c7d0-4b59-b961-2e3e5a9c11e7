import { type NextRequest, NextResponse } from "next/server";

import { stripe } from "~/utils/stripe/stripe";
import prisma from "~/server/db/prisma";
import nodemailer from "nodemailer";
import { env } from "~/env";
import { Contact, NotificationType } from "@prisma/client";
import { createSystemNotificationForAdmins } from "~/utils/notifications/createSystemNotification";

export const revalidate = 0;

export async function GET(request: NextRequest) {
  const payment_method_id = request.nextUrl.searchParams.get("paymentMethodId");
  const contactId = request.nextUrl.searchParams.get("contactId");
  const sepaHash = request.nextUrl.searchParams.get("sepaHash");

  if (!payment_method_id) {
    return NextResponse.json(
      { success: false, message: "No PaymentMethodId provided" },
      { status: 401 },
    );
  }

  if (!contactId) {
    return NextResponse.json({ success: false, message: "No contactId provided" }, { status: 401 });
  }

  const contact = await prisma.contact.findUnique({
    where: {
      id: contactId,
      sepaHash: sepaHash,
    },
  });
  if (!contact) {
    if (!contactId) {
      return NextResponse.json({ success: false, message: "No contact found" }, { status: 401 });
    }
  }

  if (contact?.stripeCustomerId) {
    const updatedCustomer = await stripe.customers.update(contact.stripeCustomerId, {
      invoice_settings: {
        default_payment_method: payment_method_id,
      },
    });
    if (updatedCustomer.invoice_settings.default_payment_method === payment_method_id) {
      //remove one time sepa has after successfully set to a company

      await prisma.contact.update({ where: { id: contactId }, data: { sepaHash: "" } });
      const transporter = nodemailer.createTransport({
        port: 465,
        host: env.EMAIL_SERVER_HOST,
        auth: {
          user: env.EMAIL_SERVER_USER,
          pass: env.EMAIL_SERVER_PASSWORD,
        },
      });
      const internalMail = await transporter.sendMail({
        from: "<EMAIL>",
        to: "<EMAIL>",
        subject: `SEPA Mandat ${contact.companyName}`,
        text: `SEPA Lastschriftmandat von hinzugefügt von: ${contact.companyName}`,
      });

      // Create admin notification for company SEPA mandate setup
      await createSystemNotificationForAdmins({
        nachricht: `Firmen-SEPA-Lastschriftmandat hinterlegt von ${contact.companyName || contact.name}`,
        type: NotificationType.INFO,
      });

      await sendNotificationMailToCustomer(contact);

      return NextResponse.json(
        { success: true, message: "Default Payment Method updated" },
        { status: 200 },
      );
    }
    return NextResponse.json(
      {
        success: false,
        message: "Error changing default payment method (update gone wrong)",
      },
      { status: 500 },
    );
  }

  return NextResponse.json(
    { success: false, message: "Error changing default payment method" },
    { status: 500 },
  );
}

const sendNotificationMailToCustomer = async (contact: Contact) => {
  const transporter = nodemailer.createTransport({
    port: 465,
    host: env.EMAIL_SERVER_HOST,
    auth: {
      user: env.EMAIL_SERVER_USER,
      pass: env.EMAIL_SERVER_PASSWORD,
    },
  });

  const externalMail = await transporter.sendMail({
    from: "<EMAIL>",
    to: env.NODE_ENV == "production" ? contact.invoiceMail : "<EMAIL>",
    subject: `Bestätigung des Empfangs Ihres SEPA-Lastschriftmandats`,
    html: `<!DOCTYPE html> <html>
        <head>
            <title>Bestätigung des Empfangs Ihres SEPA-Lastschriftmandats</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .container { background-color: #FDFDFD; padding: 20px; border-radius: 10px; }
                h1 { color: #214958; }
         
                a {font-size: large; color:#214958; }
            </style>
        </head>
        <body>
        <div class="container">
            <h1>Bestätigung des Empfangs Ihres SEPA-Lastschriftmandats</h1>
            <p>Sehr geehrte Damen und Herren,</p>
            <p>wir freuen uns, Ihnen bestätigen zu können, dass wir Ihr SEPA-Lastschriftmandat erfolgreich erhalten haben.</p>
            <p><strong>Details des SEPA-Lastschriftmandats:</strong></p>
            <p>Firma: ${contact.companyName}<br>E-Mail: ${
              contact.invoiceMail
            }<br>Datum: ${new Date().toLocaleDateString("de", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
            })}</p>
           
            <p>Das Mandat wird ab sofort für alle zukünftigen monatlichen Zahlungen der Backend-Betriebs- und Servicekosten genutzt.</p>
            <p> Falls Sie Fragen haben oder weitere Informationen benötigen, stehen wir Ihnen gerne zur Verfügung.</p>
            <p>Vielen Dank für Ihr Vertrauen und die gute Zusammenarbeit.</p>
            <p>Beste Grüße<br>Team Eulektro</p>
             <p><img
                src="data:image/png;base64,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"
                alt=""></p>
                <div class="">Eulektro GmbH <br class="">
              Werderstraße 69 <br class="">
              28199 Bremen&nbsp; <br>
            </div>
            <div class="">+49 421 17512890<br>
            </div>
            <div class=""> <a href="http://www.eulektro.de" class="">www.eulektro.de</a><br
                class="">
              <a href="mailto:<EMAIL>" class=""><EMAIL></a></div>
            Registergericht: Amtsgericht Bremen<br>
            Registernummer: HRB 36822 HB<br>
            Geschäftsführer: Jan Runkel - Jan Kahrs
            <p><br>
            </p>
        </div>
        </body>
        </html>`,
  });
};
