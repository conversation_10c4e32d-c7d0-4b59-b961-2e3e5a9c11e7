import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { pushEmpCard } from "~/utils/longship";

export const activateCard = async (cardId: string, cardNumber: string) => {
  // Get PhysicalCard uid by its number
  const physicalCard = await prisma?.physicalCard.findUnique({
    where: {
      visualNumber: cardNumber,
    },
    include: {
      EMPCard: true,
    },
  });

  if (physicalCard && (!physicalCard.EMPCard || !physicalCard?.EMPCard?.activatedAt)) {
    // Update EMPCard to link with PhysicalCard
    const updatedEmpCard = await prisma?.eMPCard.update({
      where: {
        id: cardId,
      },
      include: { tarifs: { include: { tarif: { include: { ou: true } } } }, physicalCard: true },
      data: {
        physicalCardId: physicalCard.uid,
        active: true,
        activatedAt: new Date(),
      },
    });
    if (updatedEmpCard?.physicalCard?.uid) {
      const updatedPhysical = await prisma?.physicalCard.update({
        where: {
          uid: updatedEmpCard.physicalCard.uid,
        },

        data: {
          valid: true,
        },
      });
    }
    if (updatedEmpCard) {
      return await pushEmpCard(updatedEmpCard);
    }
  }
  return ["Karte nicht gefunden"];
};

export const getEMPCardForUser = async () => {
  const session = await getServerSession(authOptions);

  if (!session) {
    return [];
  }

  const id = session?.user.id;
  const empcards = await prisma.eMPCard.findMany({
    where: {
      userId: id,
    },
    include: { physicalCard: true, tarifs: { include: { tarif: true } } },
  });
  return empcards;
};
